import { z } from "zod";
import { zString } from "../../../shared/validators";
import {
  createBlogTagValidator,
  updateBlogTagValidator,
  manageBlogValidator,
  getBlogsValidator,
  manageBlogsDashboardValidator,
  getBlogValidator,
  getTagsValidator,
  bulkUpdateBlogTagsValidator,
  bulkUpdateBlogsValidator,
} from "../../../shared/validators/blog";
import {
  manageBlog,
  createBlogTag,
  updateBlogTag,
  getTags,
  getBlogs,
  manageBlogDashboard,
  getBlog,
  deleteBlogTag,
  bulkUpdateBlogTags,
  bulkUpdateBlogs,
} from "../controller/blog.controller";
import { authProcedure } from "../middleware/auth";
import { BlogDashboardModel, BlogModel } from "../models/Blog";
import t from "../startup/trpc";

export const blogRouter = t.router({
  tags: t.procedure
    .input(getTagsValidator)
    .query(({ input }) => getTags(input)),
  createBlogTag: t.procedure
    .input(createBlogTagValidator)
    .mutation(({ input }) => createBlogTag(input)),
  updateBlogTag: t.procedure
    .input(updateBlogTagValidator)
    .mutation(({ input }) => updateBlogTag(input)),
  createBlog: t.procedure
    .input(manageBlogValidator)
    .mutation(({ input }) => manageBlog(input)),
  getBlogs: t.procedure
    .input(getBlogsValidator)
    .query(({ input }) => getBlogs(input)),
  deleteBlog: t.procedure
    .input(zString)
    .mutation(({ input }) => BlogModel.findByIdAndDelete(input)),
  getBlog: t.procedure
    .input(getBlogValidator)
    .query(({ input }) => getBlog(input)),
  manageBlogDashboard: t.procedure
    .input(manageBlogsDashboardValidator)
    .mutation(({ input }) => manageBlogDashboard(input)),
  getBlogDashboard: t.procedure.query(() =>
    BlogDashboardModel.findOne().lean()
  ),
  deleteBlogTag: t.procedure
    .input(z.string())
    .mutation(({ input }) => deleteBlogTag(input)),
  bulkUpdateBlogTags: t.procedure
    .input(bulkUpdateBlogTagsValidator)
    .mutation(({ input }) => bulkUpdateBlogTags(input)),
  bulkUpdateBlogs: t.procedure
    .input(bulkUpdateBlogsValidator)
    .mutation(({ input }) => bulkUpdateBlogs(input)),
});
