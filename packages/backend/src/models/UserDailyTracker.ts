import mongoose, { Schema } from 'mongoose';
import type { UserDailyTracker, UserQuestionAnswers } from '../../../shared/types/manage';
import UserAccountModel from './UserAccount';
import TopicModel, { optionsSchema } from './Topics';
import mixpanel from '../utils/mixpanel';
import _ from 'lodash';

const answerSchema = new Schema<UserQuestionAnswers>({
  question: { required: true, type: Schema.Types.ObjectId },
  aqi: { required: false, type: Number },
  selectedOption: { required: true, type: [Schema.Types.ObjectId] },
  answerText: { required: false, type: String },
  rawOptionData: { required: false, type: optionsSchema },
});

const schema = new Schema<UserDailyTracker>(
  {
    user: { required: true, type: Schema.Types.ObjectId, ref: UserAccountModel },
    topic: { required: true, type: Schema.Types.ObjectId, ref: TopicModel },
    date: { required: true, type: Date },
    answers: { required: true, type: [answerSchema] },
    completed: { required: false, type: Boolean, default: false },
    isDeleted: { required: false, default: false, type: Boolean },
  },
  { timestamps: true },
);

// schema.post('save', function (this) {
//   const userDailyTracker = this.toJSON();
//   mixpanel.trackEvent('ManageUserDailyTracker', _.mapValues(userDailyTracker, String), String(this.user));
// });

// schema.post('updateOne', async function (this) {
//   const updatedDocument = await this.model.findOne(this.getQuery());
//   const userDailyTracker = updatedDocument.toJSON();
//   mixpanel.trackEvent(
//     'ManageUserDailyTracker',
//     // @ts-ignore
//     _.mapValues({ ...userDailyTracker, answers: JSON.stringify(updatedDocument, null, 2) }, String, String(updatedDocument.user)),
//   );
// });

// schema.post('findOneAndUpdate', function (doc) {
//   const userDailyTracker = doc.toJSON();
//   mixpanel.trackEvent('ManageUserDailyTracker', _.mapValues(userDailyTracker, String), String(doc.user));
// });

const UserDailyTracker = mongoose.model('user-daily-tracker', schema);

schema.index({ topic: 1, user: 1 }, { unique: true });
export default UserDailyTracker;
