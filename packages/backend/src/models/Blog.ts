import { Schema, model } from "mongoose";
import {
  Blog,
  BlogDashboard,
  BlogStatus,
  Tag,
} from "../../../shared/types/Blog";

const tagSchema = new Schema<Tag>({
  tag: { required: true, type: String },
  scope: { required: false, type: [String], default: [] },
});
export const BlogTagModel = model("blog-tag", tagSchema);

const schema = new Schema<Blog>(
  {
    title: { required: true, type: String },
    author: { required: true, type: String },

    editorData: Schema.Types.Mixed,
    contentHtml: Schema.Types.Mixed,

    description: String,
    timeToReadInMins: Number,
    tags: [{ type: Schema.ObjectId, ref: BlogTagModel }],
    // diseaseTags field is deprecated, use tags instead
    banner: String,
    status: { required: true, type: String, default: BlogStatus.DRAFT },
    publishDate: { type: Date, default: new Date() },
  },
  { timestamps: true }
);

export const BlogModel = model("blog", schema);

const blogDashboardSchema = new Schema<BlogDashboard>({
  bannerImage: String,
  title: String,
});

export const BlogDashboardModel = model("blog-dashboard", blogDashboardSchema);
