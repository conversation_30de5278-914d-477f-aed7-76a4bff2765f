import React, { useRef, useState } from "react";
import * as XLSX from "xlsx";
import { Button } from "@web/components/ui/button";
import { Input } from "@web/components/ui/input";
import { Label } from "@web/components/ui/label";
import { Upload, FileSpreadsheet, X } from "lucide-react";

interface ExcelFileUploadProps {
  onDataParsed: (data: ExcelData) => void;
  onError?: (error: string) => void;
}

interface ExcelData {
  sheets: {
    [sheetName: string]: any[][];
  };
  sheetNames: string[];
}

export function ExcelFileUpload({
  onDataParsed,
  onError,
}: ExcelFileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedData, setParsedData] = useState<ExcelData | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setParsedData(null); // Clear previous parsed data
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) {
      onError?.("Please select a file first");
      return;
    }

    if (
      !selectedFile.name.endsWith(".xlsx") &&
      !selectedFile.name.endsWith(".xls")
    ) {
      onError?.("Please select a valid Excel file (.xlsx or .xls)");
      return;
    }

    setIsProcessing(true);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: "array" });

      console.log("Excel file loaded successfully");
      console.log("Sheet names:", workbook.SheetNames);

      const sheets: { [sheetName: string]: any[][] } = {};

      workbook.SheetNames.forEach((sheetName) => {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: "",
          raw: false,
        });
        sheets[sheetName] = jsonData as any[][];

        console.log(`Sheet "${sheetName}" data:`, jsonData);
      });

      const excelData: ExcelData = {
        sheets,
        sheetNames: workbook.SheetNames,
      };

      console.log("Complete Excel data structure:", excelData);
      setParsedData(excelData);
    } catch (error) {
      console.error("Error reading Excel file:", error);
      onError?.(
        "Error reading Excel file. Please make sure it's a valid Excel file."
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const clearFile = () => {
    setSelectedFile(null);
    setParsedData(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-4 p-4 border border-dashed border-gray-300 rounded-lg">
      <div className="flex items-center space-x-2">
        <FileSpreadsheet className="h-5 w-5 text-green-600" />
        <Label htmlFor="excel-upload" className="text-sm font-medium">
          Upload Excel File
        </Label>
      </div>

      <div className="space-y-3">
        <Input
          ref={fileInputRef}
          id="excel-upload"
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileSelect}
          className="cursor-pointer"
        />

        {selectedFile && (
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <span className="text-sm text-gray-700">
              {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
            </span>
            <Button type="button" variant="ghost" size="sm" onClick={clearFile}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        <div className="flex space-x-2">
          <Button
            type="button"
            onClick={handleFileUpload}
            disabled={!selectedFile || isProcessing}
            className="flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>{isProcessing ? "Processing..." : "Parse Excel File"}</span>
          </Button>

          {parsedData && (
            <Button
              type="button"
              onClick={() => onDataParsed(parsedData)}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
            >
              <span>Import Data to Form</span>
            </Button>
          )}
        </div>

        {parsedData && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
            <h4 className="font-medium text-green-800 mb-2">
              Excel File Parsed Successfully!
            </h4>
            <p className="text-sm text-green-700">
              Found {parsedData.sheetNames.length} sheets:{" "}
              {parsedData.sheetNames.join(", ")}
            </p>
            <p className="text-sm text-green-600 mt-1">
              Click "Import Data to Form" to populate the form with this data.
            </p>
          </div>
        )}
      </div>

      <div className="text-xs text-gray-500">
        <p>Supported formats: .xlsx, .xls</p>
        <p>
          The file should contain sheets named: "Subscription Questions", "Daily
          Tracker Questions", "Learning Questions"
        </p>
      </div>
    </div>
  );
}
