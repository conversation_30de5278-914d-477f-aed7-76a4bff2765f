"use client";

import { useFormContext } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@web/components/ui/form";
import MultipleSelector, { Option } from "@web/components/ui/multi-select";
import { Form } from "../../../../@types/Form";

type Props = Form & {
  options: Option[];
  defaultOptions?: Option[];
  placeholder?: string;
  disabled?: boolean;
  maxSelected?: number;
  creatable?: boolean;
  hidePlaceholderWhenSelected?: boolean;
  emptyIndicator?: React.ReactNode;
  loadingIndicator?: React.ReactNode;
  badgeClassName?: string;
  groupBy?: string;
  onSearch?: (value: string) => Promise<Option[]>;
  onSearchSync?: (value: string) => Option[];
  onMaxSelected?: (maxLimit: number) => void;
  delay?: number;
  triggerSearchOnFocus?: boolean;
  hideClearAllButton?: boolean;
};

export function FormMultiSelect({
  name,
  label,
  description,
  options,
  defaultOptions,
  placeholder,
  disabled,
  maxSelected,
  creatable,
  hidePlaceholderWhenSelected,
  emptyIndicator,
  loadingIndicator,
  badgeClassName,
  groupBy,
  onSearch,
  onSearchSync,
  onMaxSelected,
  delay,
  triggerSearchOnFocus,
  hideClearAllButton,
}: Props) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem>
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <MultipleSelector
                value={(Array.isArray(field.value) ? field.value : []).map(
                  (item: any) => {
                    if (typeof item === "string") {
                      // Find the matching option to get the label
                      const matchingOption = options.find(
                        (option) => option.value === item
                      );
                      return {
                        value: item,
                        label: matchingOption ? matchingOption.label : item,
                      };
                    }
                    // If it's already an Option object, return it as is
                    return item;
                  }
                )}
                onChange={(v) => {
                  console.log("MultiSelect onChange:", v);
                  field.onChange(v.map((item) => item.value));
                }}
                options={options}
                defaultOptions={defaultOptions}
                placeholder={placeholder}
                disabled={disabled}
                maxSelected={maxSelected}
                creatable={creatable}
                hidePlaceholderWhenSelected={hidePlaceholderWhenSelected}
                emptyIndicator={emptyIndicator}
                loadingIndicator={loadingIndicator}
                badgeClassName={badgeClassName}
                groupBy={groupBy}
                onSearch={onSearch}
                onSearchSync={onSearchSync}
                onMaxSelected={onMaxSelected}
                delay={delay}
                triggerSearchOnFocus={triggerSearchOnFocus}
                hideClearAllButton={hideClearAllButton}
              />
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
