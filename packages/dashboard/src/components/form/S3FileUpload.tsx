import { uploadToS3 } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { generatedSignedUrlValidator } from "packages/shared/validators/lib.validators";
import { ChangeEvent, ChangeEventHandler, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { z } from "zod";
import ArrowPathIcon from "@heroicons/react/20/solid/ArrowPathIcon";

export default function S3FileUpload({
  name,
  disabled,
  ...props
}: Omit<z.infer<typeof generatedSignedUrlValidator>, "ext"> & {
  name: string;
  disabled?: boolean;
}) {
  const [uploadStatus, setUploadStatus] = useState(false);
  const s3SignedUrl = trpc.lib.servicesS3SignedUrl.useMutation();

  return (
    <div className="flex flex-row items-center gap-3">
      {(s3SignedUrl.isLoading || uploadStatus) && (
        <ArrowPathIcon className="w-8 h-8 text-primary animate-spin" />
      )}
      <Controller
        name={name}
        render={({ field: { onChange } }) => {
          const handleImageUpload = async (
            event: ChangeEvent<HTMLInputElement>
          ) => {
            try {
              setUploadStatus(true);
              const { signedUrl, s3FileUrl } = await s3SignedUrl.mutateAsync({
                ext: "png",
                ...props,
              });

              if (event.target.files && event.target.files.length > 0) {
                uploadToS3({ signedUrl, file: event.target.files[0] });
                setUploadStatus(false);
              }

              onChange(s3FileUrl);
            } catch (ex) {
              setUploadStatus(false);
            }
          };

          return (
            <input
              type="file"
              onChange={handleImageUpload}
              disabled={disabled}
            />
          );
        }}
      />
    </div>
  );
}
