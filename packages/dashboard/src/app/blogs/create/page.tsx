"use client";
import { BlockNoteView, useCreateBlockNote } from "@blocknote/react";
import React, { useMemo, useState } from "react";
import { z } from "zod";
import {
  createBlogTagValidator,
  manageBlogValidator,
} from "packages/shared/validators/blog";
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@web/providers/Providers";
import FormField from "@web/components/form/FormField";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON>T<PERSON>le,
  DialogTrigger,
} from "@web/components/ui/dialog";
import { Button } from "@web/components/ui/button";
import { toastPromise } from "@web/lib/utils";
import { FormDatePicker } from "@web/components/form/FormDatePicker";
import { FormSelect } from "@web/components/form/FormSelect";
import { BlogStatus } from "packages/shared/types/Blog";
import { Label } from "@web/components/ui/label";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { ScrollArea } from "@web/components/ui/scroll-area";
import ManageBlogPostForm from "@web/elements/form/ManageBlogPost";
import MarkdownPreview from "@uiw/react-markdown-preview";
import { useRouter } from "next/navigation";
import { Input } from "@web/components/ui/input";

type BlogForm = z.infer<typeof manageBlogValidator>;

type BlogTagForm = z.infer<typeof createBlogTagValidator>;

export default function Learn() {
  const router = useRouter();
  const [blogTagModal, setBlogTagModal] = useState(false);
  const methods = useForm<BlogForm>({
    resolver: zodResolver(manageBlogValidator),
    defaultValues: {
      author: "",
      banner: "",
      contentHtml: "",
      description: "",
      publishDate: "" as unknown as Date,
      status: BlogStatus.DRAFT,
      tags: [],
      timeToReadInMins: "" as unknown as number,
      title: "",
      editorData: [],
      // diseaseTags field is deprecated, use tags instead
    },
  });

  const createBlogTagsMethod = useForm<BlogTagForm>({
    resolver: zodResolver(createBlogTagValidator),
    defaultValues: {
      tag: "",
      scope: [],
    },
  });

  const createBlog = trpc.blog.createBlog.useMutation();
  const createBlogTag = trpc.blog.createBlogTag.useMutation();

  const blogTags = trpc.blog.tags.useQuery();

  const handleCreateBlogTagSubmit = createBlogTagsMethod.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createBlogTag.mutateAsync(data),
      success: "Tag Created Succesfully ",
      onSuccess: () => {
        createBlogTagsMethod.reset();
        blogTags.refetch();
        setBlogTagModal(false);
      },
    })
  );
  const handleCreateBlogSubmit: SubmitHandler<BlogForm> = (data) => {
    console.log("=== BLOG CREATE SUBMISSION DEBUG ===");
    console.log("Raw form data:", data);
    console.log("data.tags type:", typeof data.tags);
    console.log("data.tags value:", data.tags);
    console.log("Is data.tags an array?", Array.isArray(data.tags));

    // Handle both string array and object array from FormMultiSelect
    let tagNames: string[] = [];
    if (Array.isArray(data.tags)) {
      tagNames = data.tags.map((tag: any) => {
        // Handle both object format {value: 'tagName', label: 'tagName'} and string format
        if (typeof tag === "object" && tag.value) {
          return tag.value;
        }
        return typeof tag === "string" ? tag : String(tag);
      });
    }

    console.log("Processed tagNames:", tagNames);

    // Map tag names to tag IDs
    const selectedTagIds = tagNames
      .map((tagName) =>
        blogTags.data?.find((tag) => tag.tag === tagName)?._id.toString()
      )
      .filter((id): id is string => Boolean(id));

    console.log("selectedTagIds:", selectedTagIds);

    const finalData = {
      ...data,
      tags: selectedTagIds,
    };

    console.log("Final data being sent to backend:", finalData);
    console.log("=== END CREATE DEBUG ===");

    return toastPromise({
      asyncFunc: createBlog.mutateAsync(finalData),
      success: "Blog Created Succesfully ",
      onSuccess: () => {
        methods.reset();
        router.replace("/blogs");
      },
    });
  };

  return (
    <div className="flex h-[calc(100vh-105px)] gap-5 mt-7 ">
      <div className="flex gap-4">
        <div className="ml-4 mb-5">
          <h2 className="text-primary text-xl font-semibold">Create Blog</h2>
          <ScrollArea className="h-[calc(100vh-200px)] w-[38vw] p-4 border">
            <ManageBlogPostForm
              methods={methods}
              blogTags={blogTags.data || []}
              onFormSubmit={handleCreateBlogSubmit}
            />
          </ScrollArea>
        </div>
        <div>
          <h2 className="text-primary text-xl font-semibold">
            Blog Content Preview
          </h2>
          <ScrollArea className="h-[calc(100vh-200px)] w-[300px] p-4 border">
            <MarkdownPreview
              source={methods.watch("contentHtml")}
              className="bg-white"
              wrapperElement={{ "data-color-mode": "light" }}
            />
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}
