"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { Button } from "@web/components/ui/button";
import { trpc } from "@web/providers/Providers";
import { useSearchParams } from "next/navigation";
import useStoreRedirect from "packages/dashboard/hooks/useStoreRedirect";
import { useEffect, useState, useRef } from "react";

export default function Download() {
  const [canRedirect, setCanRedirect] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [timer, setTimer] = useState(3);
  const eventProcessedRef = useRef(false);

  const params = useSearchParams();
  const { mutateAsync: createActonEvent } =
    trpc.mixpanel.createActionEvent.useMutation();
  const { mutateAsync: createUtmAnalytics } =
    trpc.utm.createUtmAnalytics.useMutation();

  useEffect(() => {
    const createEvent = async () => {
      // Use ref to ensure this only runs once across re-renders
      if (eventProcessedRef.current) return;
      eventProcessedRef.current = true;

      const utmParams: Record<string, string> = {};
      let utmId: string | null = null;

      // Extract UTM parameters from URL
      params.forEach((value, key) => {
        if (!value) return;

        // Store the utm_id separately if it exists
        if (key === "utm_id") {
          utmId = value;
        } else {
          utmParams[key] = value;
        }
      });

      console.log("UTM Params:", utmParams);

      // Only proceed if we have parameters to track
      if (Object.keys(utmParams).length > 0) {
        // Track event in Mixpanel
        await createActonEvent({
          action: "WEB_UTM_APP_DOWNLOADS",
          metadata: utmParams,
        });

        // If we have a UTM ID, save analytics
        if (utmId) {
          try {
            // Create UTM analytics record
            await createUtmAnalytics({
              utm_id: utmId,
              utm_source: utmParams.utm_source,
              utm_medium: utmParams.utm_medium,
              utm_campaign: utmParams.utm_campaign,
              utm_term: utmParams.utm_term,
              utm_content: utmParams.utm_content,
            });
            console.log("UTM analytics saved successfully");
          } catch (error) {
            console.error("Failed to save UTM analytics:", error);
          }
        }
      }

      setShowModal(true);
    };

    createEvent();
  }, [params, createActonEvent, createUtmAnalytics]);

  useEffect(() => {
    const sub = setInterval(() => {
      setTimer((t) => t - 1);
    }, 1000);

    return () => {
      clearInterval(sub);
    };
  }, []);

  useEffect(() => {
    if (timer === 0) {
      setCanRedirect(true);
    }
  }, [timer]);

  useStoreRedirect(canRedirect);

  return (
    <AlertDialog open={showModal} onOpenChange={setShowModal}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Redirecting to app store in ...{timer}s
          </AlertDialogTitle>
        </AlertDialogHeader>
      </AlertDialogContent>
    </AlertDialog>
  );
}
