import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  Dialog<PERSON>ooter,
  DialogHeader,
} from '@web/components/ui/dialog';
import * as z from 'zod';
import CreateCategory from './services/CreateCategory';
import CreateService from './services/CreateService';
import { Button } from '@web/components/ui/button';
import { ScrollArea } from '@web/components/ui/scroll-area';
import { UseFormReturn } from 'react-hook-form';
import { createOrUpdateServiceValidator } from 'packages/shared/validators/service.validator';
import { DialogProps } from '@radix-ui/react-dialog';

type Form = z.infer<typeof createOrUpdateServiceValidator>;
type Props = {
  methods: UseFormReturn<Form>
} & DialogProps

export default function CreateServiceDialog({ methods, ...rest }: Props) {
  const serviceId = methods.watch("_id");
  return (
    <Dialog {...rest}>
      <DialogContent className="min-w-[80vw]">
        <ScrollArea className="h-[80vh]">
          <DialogHeader>Services</DialogHeader>
          <div className="flex">
            <div className="flex flex-col flex-1 border-r pr-4">
              <CreateService methods={methods} />
            </div>
            {/* <div className="flex flex-col flex-1">
              <CreateCategory isEditMode={!!serviceId} />
            </div> */}
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button>Close</Button>
            </DialogClose>
          </DialogFooter>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
