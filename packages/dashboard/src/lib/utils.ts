import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { toast } from 'react-hot-toast';
import { TRPCClientError } from '@trpc/client';

interface ToastPromiseParams {
  asyncFunc: Promise<unknown>;
  success?: string;
  error?: string;
  loading?: string;
  onSuccess?: (data: any) => void;
  onError?: (err: any) => void;
}
export function toastPromise<T>({
  asyncFunc,
  error = 'Something went wrong...',
  success = 'Success',
  loading = 'Loading...',
  onError,
  onSuccess,
}: ToastPromiseParams) {
  return toast.promise(asyncFunc, {
    loading,
    success: (data) => {
      onSuccess?.(data);
      return success;
    },
    error: (err) => {
      onError?.(err);
      if (err instanceof TRPCClientError) return err.message;
      else return error;
    },
  }) as T;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function nameToInitials(name: string) {
  const [firstName, lastname] = name.split(' ');
  return `${firstName[0]}${lastname ? lastname[0] : firstName[1]}`;
}

export function getDayHoursMins() {
  const timeOfDay = ['AM', 'PM'];
  const hours = new Array(12).fill(0).map((_, idx) => idx + 1);
  const mins = [];

  for (let i = 0; i < 60; i = i + 5) mins.push(i);

  return { timeOfDay, hours, mins };
}

export async function uploadToS3({
  file,
  signedUrl,
}: {
  signedUrl: string;
  file: File;
}) {
  try {
    let headers = new Headers();
    headers.append('Content-Type', 'image/png');

    await fetch(signedUrl, {
      method: 'PUT',
      headers,
      body: file,
      redirect: 'follow',
    });
  } catch (ex) {
    console.log(ex);
  }
}

export const imgUrl = (path: string) => process.env.NEXT_PUBLIC_S3_URL! + path;
