import * as z from "zod";
import { zNumber, zString } from ".";
import { BlogStatus, BlogType } from "../types/Blog";

export const createBlogTagValidator = z.object({
  tag: zString,
  scope: z.array(zString).optional().default([]),
});

export const updateBlogTagValidator = z.object({
  tagId: zString,
  tag: zString.optional(),
  scope: z.array(zString).optional(),
});

export const manageBlogValidator = z.object({
  blogId: z.string().optional(),
  title: zString,
  contentHtml: zString,
  editorData: z.any(),
  author: zString,
  description: zString.optional(),
  timeToReadInMins: zNumber,
  tags: z.array(zString),
  // diseaseTags is deprecated, use tags instead
  diseaseTags: z.array(zString).optional(),
  banner: zString,
  status: z.nativeEnum(BlogStatus),
  publishDate: z.date().optional().or(z.string().optional()),
});

export const getBlogsValidator = z
  .object({
    tags: z.array(zString).optional(),
    // diseaseTags is deprecated, use tags instead
    diseaseTags: z.array(zString).optional(),
    status: z.nativeEnum(BlogStatus).optional(),
    author: zString.optional(),
    type: z.nativeEnum(BlogType).optional(),
    publishDate: z.date().optional().or(z.string().optional()),
  })
  .optional();

export const getBlogValidator = z.object({ blog: zString });

export const manageBlogsDashboardValidator = z.object({
  bannerImage: zString,
  title: zString,
});

export const getTagsValidator = z
  .object({
    scope: z.array(zString).optional(),
  })
  .optional();

export const bulkUpdateBlogTagsValidator = z.object({
  blogIds: z.array(zString),
  tagIds: z.array(zString),
});

export const bulkUpdateBlogsValidator = z.object({
  blogIds: z.array(zString),
  tagIds: z.array(zString).optional(),
  status: z.nativeEnum(BlogStatus).optional(),
  publishDate: z.date().optional().or(z.string().optional()),
});
