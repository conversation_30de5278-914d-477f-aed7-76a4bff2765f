import React from "react";
import Pressable from "@components/common/Pressable";
import { Image } from "react-native-expo-image-cache";
import AppText from "@components/common/AppText";
import { View, StyleSheet, Dimensions, Text } from "react-native";
import { imageKit } from "@utils/index";
import Svg, { Path, SvgProps } from "react-native-svg";
import { Globe2, Lock } from "lucide-react-native";

const { width } = Dimensions.get("screen");
const AVATAR_SIZE = 56;

interface PostOrGroupCardProps {
  onPress?: (v?: string) => void;
  imageUrl: string;
  name: string;
  date: string;
  message: string;
  privacy?: string;
}

function DefaultIcon(props: SvgProps) {
  return (
    <Svg
      width="66px"
      height="66px"
      viewBox="0 0 66 66"
      fill="none"
      preserveAspectRatio="xMidYMid meet"
      {...props}
    >
      <Path
        d="M0 10C0 4.477 4.477 0 10 0h46c5.523 0 10 4.477 10 10v46c0 5.523-4.477 10-10 10H10C4.477 66 0 61.523 0 56V10z"
        fill="#004987"
      />
      <Path
        d="M57.543 16.752l-.98 10.09c-.543 5.57-5.983 9.697-12.147 9.206l-11.169-.885.98-10.09c.543-5.569 5.983-9.697 12.147-9.205l11.169.884z"
        fill="#fff"
      />
      <Path
        d="M48.099 46.105c-.38 3.93-4.225 6.846-8.576 6.502-4.352-.344-7.579-3.816-7.198-7.747l.689-7.126 7.887.623c4.351.344 7.579 3.816 7.198 7.748zM10.023 13l-.98 10.09c-.543 5.57 4.026 10.483 10.19 10.975l11.17.884.978-10.09c.544-5.57-4.025-10.483-10.19-10.975L10.024 13z"
        fill="#FF8E1C"
      />
      <Path
        d="M13.684 43.384c-.381 3.931 2.846 7.404 7.198 7.748 4.351.344 8.195-2.572 8.575-6.503l.69-7.125-7.887-.623c-4.352-.344-8.178 2.572-8.576 6.503z"
        fill="#fff"
      />
    </Svg>
  );
}

export function PostOrGroupCard({
  onPress,
  imageUrl,
  name,
  date,
  message,
  privacy,
}: PostOrGroupCardProps) {
  return (
    <Pressable onPress={onPress} style={styles.card}>
      {imageUrl ? (
        <Image
          uri={imageKit({
            imagePath: imageUrl,
            transform: ["w-500"],
          })}
          style={styles.avatar}
        />
      ) : (
        <View
          style={{
            ...styles.avatar,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <DefaultIcon />
        </View>
      )}
      <View style={styles.textContainer}>
        {name ? (
          <View style={styles.nameRow}>
            <Text style={styles.name}>{name}</Text>
            <Text style={styles.date}>{date}</Text>
          </View>
        ) : null}
        {privacy && (
          <View className="flex-row items-center gap-2">
            {privacy === "private" ? (
              <Lock color="#aeaeae" size={14} />
            ) : (
              <Globe2 color="#aeaeae" size={14} />
            )}
            <Text className="text-[#24588A]">{privacy}</Text>
          </View>
        )}
        <View style={styles.messageContainer}>
          <Text numberOfLines={2} ellipsizeMode="tail" style={styles.message}>
            {message}
          </Text>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#F5F7F9",
    borderRadius: 10,
    paddingHorizontal: 0,
    paddingVertical: 10,
    marginVertical: 8,
    marginHorizontal: 8,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 8 },
    // shadowOpacity: 0.15,
    // shadowRadius: 8,
    boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
    elevation: 4,
    width: width * 0.9,
    alignSelf: "center",
    // minHeight: AVATAR_SIZE,
    height: 83,
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 85,
    height: 83,
    overflow: "hidden",
    backgroundColor: "#fff",
    // alignItems: "center",
    // justifyContent: "center",
    borderRadius: 10,
    // marginRight: 16,
    boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
    paddingLeft: 12,
  },
  messageContainer: {
    flex: 1,
    justifyContent: "center",
    paddingRight: 6,
  },
  name: {
    fontWeight: 700,
    fontSize: 14,
    color: "#336D9F",
    fontFamily: "Montserrat",
  },
  nameRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginRight: 6,
  },
  date: {
    fontSize: 8,
    color: "#0B79D3",
    alignSelf: "flex-start",
    marginLeft: 8,
    fontWeight: 600,
  },
  message: {
    fontSize: 12,
    color: "#24588A",
    lineHeight: 18,
    flexShrink: 1,
    flexWrap: "wrap",
    fontWeight: 400,
    marginBottom: 8,
    fontFamily: "Montserrat",
  },
});
