import React, { useState, useEffect } from "react";
import { View, Text } from "react-native";

import Slider from "@react-native-community/slider";
import { Option } from "../../../../shared/types/manage";

type Props = {
  options: Option[] | undefined;
  onValueChange: (value: number) => void;
};

export default function ManageSliderQuestion({
  options,
  onValueChange,
}: Props) {
  const [value, setValue] = useState(0);
  const [minValue, setMinValue] = useState(0);
  const [maxValue, setMaxValue] = useState(5);

  useEffect(() => {
    if (options && options.length > 0) {
      const values = options.map((opt) => parseInt(opt.value));
      setMinValue(Math.min(...values));
      setMaxValue(Math.max(...values));
      setValue(minValue);
    }
  }, [options]);

  const handleValueChange = (val: number) => {
    const roundedVal = Math.round(val);
    setValue(roundedVal);
    onValueChange(roundedVal);
  };

  return (
    <View className="flex-1 justify-center items-center bg-white px-4">
      <Text className="text-2xl font-bold text-gray-800 mb-5">{value}</Text>

      <Slider
        style={{
          width: 350,
          height: 40,
        }}
        value={value}
        onValueChange={handleValueChange}
        minimumValue={minValue}
        maximumValue={maxValue}
        step={1}
        minimumTrackTintColor="#1E90FF"
        maximumTrackTintColor="#d3d3d3"
        thumbTintColor="#1E90FF"
      />
    </View>
  );
}
