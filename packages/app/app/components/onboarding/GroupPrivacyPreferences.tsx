import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState, useEffect } from "react";
import { Dimensions, View, Text } from "react-native";
import BookIcon from "@assets/svg/BookIcon";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import AppButton from "@components/common/AppButton";
import Svg, {
  Path,
  Defs,
  Pattern,
  Use,
  Image,
  SvgProps,
} from "react-native-svg";
import useImagePicker from "@hooks/useImagePicker";
import AppText from "@components/common/AppText";
import AppFormCheckbox from "@components/form/AppFormCheckbox";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RABBLEGROUP_TYPES } from "../../../../shared/validators/rabblegroup.validator";
import { Platform } from "react-native";
import mixpanel from "@utils/mixpanel";

function LockIcon(props: SvgProps) {
  return (
    <Svg width={60} height={61} viewBox="0 0 60 61" fill="none" {...props}>
      <Path fill="url(#pattern0_10483_20199)" d="M0 0H60V61H0z" />
      <Defs>
        <Pattern
          id="pattern0_10483_20199"
          patternContentUnits="objectBoundingBox"
          width={1}
          height={1}
        >
          <Use
            xlinkHref="#image0_10483_20199"
            transform="scale(.01667 .0164)"
          />
        </Pattern>
        <Image
          id="image0_10483_20199"
          width={60}
          height={61}
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </Defs>
    </Svg>
  );
}

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z
  .object({
    isPublic: z.boolean(),
    isPrivate: z.boolean(),
    appearanceInDirectory: z
      .string()
      .refine(
        (val) => val.toLowerCase() === "yes" || val.toLowerCase() === "no",
        {
          message: "Please enter 'yes' or 'no'",
        }
      ),
  })
  .refine((data) => data.isPublic || data.isPrivate, {
    message: "You must select either Public or Private",
    path: ["isPublic"],
  });

type Form = z.infer<typeof schema>;

export default function GroupPrivacyPreferences() {
  const navigation = useNavigation<RootNavigationProp>();
  const { params } = useRoute<RouteProp<RootNavParams, "CreateNewRabble">>();

  const { groupId } =
    useRoute<RouteProp<RootNavParams, "GroupPrivacyPreferences">>()?.params;
  const [logo, setLogo] = useState("");

  const { user } = useSession();
  const { images, imagePicker } = useImagePicker();
  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      isPrivate: false,
      isPublic: false,
      appearanceInDirectory: "",
    },
  });

  const { mutateAsync: updatedRabbleGroup } =
    trpc.rabbleGroups.updateRabbleGroupPatials.useMutation();

  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery(
    { group: groupId },
    { enabled: !!groupId }
  );

  const { handleSubmit, formState, setValue, watch } = methods;
  const isPublic = watch("isPublic");
  const isPrivate = watch("isPrivate");

  useEffect(() => {
    if (isPublic && isPrivate) {
      setValue("isPrivate", false);
    }
  }, [isPublic]);

  useEffect(() => {
    if (isPrivate && isPrivate) {
      setValue("isPublic", false);
    }
  }, [isPrivate]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      console.log("navigated", data, groupId);
      await updatedRabbleGroup({
        id: String(groupId),
        privacy: data?.isPublic
          ? RABBLEGROUP_TYPES.public
          : RABBLEGROUP_TYPES.private,
        appearanceInDirectory:
          data?.appearanceInDirectory?.toLowerCase() === "yes",
      });
      mixpanel.trackEvent(
        "Rabble community status selected (Step 3)(Inviting)",
        {
          community_name: groupInfo?.data?.groupName || "",
          community_desc: groupInfo?.data?.groupDescription || "",
          community_guideline: groupInfo?.data?.groupGuidelines || "",
          community_status: data?.isPublic
            ? RABBLEGROUP_TYPES.public
            : RABBLEGROUP_TYPES.private,
          email_or_phone: user?.email || user?.contact?.phone || "",
        },
        user?._id?.toString(),
        "v2"
      );
      navigation.navigate("RabbleTagAssociation", {
        groupId,
        fromConnectScreen: params?.fromConnectScreen,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  });

  // const onPress = () => {
  //   navigation.navigate("RabbleTagAssociation");
  // }

  return (
    <Screen>
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            // @ts-expect-error
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={3}
            current={1}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-1 items-center mt-[-12px]">
          <View className="flex-row items-center">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt message="Let's align to your privacy preferences." />
          </View>
          <View className="bg-[#E1F1FF] p-4 h-[91px] w-[94px] rounded-xl items-center shadow-md shadow-[#00000040]">
            <LockIcon />
          </View>
          <Text className="mt-2 text-center text-[#336D9F] text-center font-[Montserrat] text-[12px] font-bold leading-[130%]">
            Privacy
          </Text>
          <View
            className="flex-1 p-2 mt-[-56px]"
            style={{ width: cardWidth, gap: 8 }}
          >
            <FormProvider {...methods}>
              <View
                className="flex-1 justify-between"
                style={{ minHeight: 400 }}
              >
                <View className="mt-16">
                  <AppText
                    className="text-[#004987] mb-2 mt-6"
                    style={{
                      fontFamily: "Montserrat",
                      fontSize: Platform.OS === "android" ? 20 : 18,
                      fontWeight: Platform.OS === "android" ? "500" : "500",
                      lineHeight: 24,
                    }}
                  >
                    Is your rabble intended to be public or private?
                  </AppText>

                  <AppFormCheckbox
                    name="isPublic"
                    large
                    label={
                      <AppText
                        className="text-[#004987]"
                        style={{
                          fontFamily: "Montserrat",
                          fontSize: 18,
                          fontWeight: "300",
                          lineHeight: 24,
                        }}
                      >
                        <Text
                          className="text-[#004987] font-[Montserrat] text-[18px] font-medium leading-none"
                          style={{
                            fontFamily: "Montserrat",
                            fontSize: Platform.OS === "android" ? 20 : 18,
                            lineHeight: 24,
                          }}
                        >
                          Public:
                        </Text>{" "}
                        Anyone can find and request to join.
                      </AppText>
                    }
                  />
                  <View className="mt-8">
                    <AppFormCheckbox
                      name="isPrivate"
                      large
                      label={
                        <AppText
                          className="text-[#004987]"
                          style={{
                            fontFamily: "Montserrat",
                            fontSize: 18,
                            fontWeight: "300",
                            lineHeight: 24,
                          }}
                        >
                          <Text
                            className="text-[#004987] font-[Montserrat] text-[18px] font-medium leading-none"
                            style={{
                              fontFamily: "Montserrat",
                              fontSize: Platform.OS === "android" ? 20 : 18,
                              lineHeight: 24,
                            }}
                          >
                            Private:
                          </Text>{" "}
                          Only invited members can see or join the group.
                        </AppText>
                      }
                    />
                  </View>
                  <View className="mt-8">
                    <AppFormInput
                      name="appearanceInDirectory"
                      label="Should it appear in our directory?"
                      labelClass={`text-[#004987] font-[Montserrat] ${
                        Platform.OS === "android"
                          ? "text-[20px]"
                          : "text-[18px]"
                      } font-medium leading-none`}
                      fieldClass=""
                      placeholderTextColor="#B4DDFF"
                      placeholder="Yes or No"
                      borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                      className="rounded-lg text-[18px]"
                    />
                  </View>
                </View>
              </View>
            </FormProvider>
          </View>
        </View>
      </AppScrollView>
      <View className="flex flex-row items-center justify-between mb-2 mx-4">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant={formState?.isValid ? "new-primary" : "disabled"}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          disabled={!formState.isValid}
          style={
            formState?.isValid
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          onPress={onSubmit}
        />
      </View>
    </Screen>
  );
}
