import AppText from "@components/common/AppText";
import { LearnNavParams } from "@navigation/learn-navigator/LearnNavParams";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { useMemo } from "react";
import { Image, StyleSheet, View } from "react-native";
import { Blog } from "../../../../shared/types/Blog";
// @ts-ignore
import Pressable from "@components/common/Pressable";
import Time from "@assets/svg/services/Time";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

type Props = {
  blog: Blog;
};

export default function BlogListItem(props: Props) {
  const navigation = useNavigation<StackNavigationProp<LearnNavParams>>();
  const { user } = useSession();

  const onBlogItemPress = async () => {
    await mixpanel.trackEvent(
      "Learn post card clicked",
      {
        screen_name: "Learn post list",
        post_name: props?.blog?.title || "",
        post_id: props?.blog?._id?.toString() || "",
        email: user?.email || "",
        phone: user?.contact?.phone || "",
      },
      String(user?._id),
      "v2"
    );
    navigation.navigate("BlogDetailsScreen", { blog: String(props.blog._id) });
  };

  const timeToRead = useMemo(() => {
    const { timeToReadInMins } = props.blog;
    if (!timeToReadInMins) return "";
    if (timeToReadInMins > 60) {
      return `${(timeToReadInMins / 60).toFixed(1)}h`;
    }
    return `${timeToReadInMins}mins`;
  }, [props.blog.timeToReadInMins]);

  return (
    <Pressable
      actionTag="service card item"
      className="bg-white rounded-lg p-4 mt-2"
      style={{
        elevation: 4,
        shadowColor: "#004987",
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }}
      onPress={onBlogItemPress}
    >
      <View className="flex flex-row">
        <Image
          className="w-[60px] h-[42px] rounded-md mr-4"
          source={{ uri: props?.blog?.banner }}
          resizeMode="cover"
        />
        <View className="flex flex-col justify-center max-w-[80%] ">
          <AppText
            numberOfLines={2}
            ellipsizeMode="tail"
            className="text-base text-primary font-montserratSemiBold overflow-hidden whitespace-nowrap mr-2"
          >
            {props.blog.title}
          </AppText>

          <View className="flex flex-row items-center">
            <AppText className="text-sm text-neutral-500">{timeToRead}</AppText>
          </View>
        </View>
      </View>

      <View className="mt-2 text-[#333941]">
        <AppText numberOfLines={2}>{props.blog.description}</AppText>
        <AppText className="text-accent font-montserratBold self-end">
          Read More
        </AppText>
      </View>
    </Pressable>
  );

  return (
    <Pressable onPress={onBlogItemPress} actionTag="blog list item">
      <View
        className="mt-6 rounded-xl h-auto bg-white my-2"
        style={[styles.container]}
      >
        <View className="flex">
          {!!props?.blog?.banner && (
            <Image
              source={{ uri: props?.blog?.banner }}
              className="rounded-t-xl"
              style={{ height: 230, width: "100%", objectFit: "cover" }}
            />
          )}
          <View className="justify-between p-3" style={{ flex: 3 }}>
            <AppText
              className="text-md text-[#191D23]  font-montserratSemiBold mr-2"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {props.blog.title}
            </AppText>
            <AppText
              numberOfLines={3}
              className="text-sm text-[#191D23]  font-montserratMedium mr-2"
            >
              {props.blog.description}
            </AppText>
            <View className="flex justify-between">
              <View style={{ flex: 1 }}>
                <AppText className="mt-3 text-xs text-[#191D23] font-montserratRegular">
                  {timeToRead}
                </AppText>
              </View>
              <View style={{ flex: 3 }}></View>
            </View>
          </View>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  likeImageStyles: { borderWidth: 2, borderColor: "#fff" },
});
