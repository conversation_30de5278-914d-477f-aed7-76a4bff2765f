import Screen from "@components/common/Screen";
import { useEffect } from 'react';
import { <PERSON><PERSON>, ScrollView, Share, StyleSheet, View } from "react-native";
import Banner from "@components/learn/Banner";
import { LearnNavParams } from "@navigation/learn-navigator/LearnNavParams";
import { RouteProp, useRoute } from "@react-navigation/native";
import Title from "@components/learn/Title";
import AppText from "@components/common/AppText";
import { ENV } from "@constants/index";
import { Share2Icon } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import { format } from "date-fns";
import Pressable from "@components/common/Pressable";
import MarkDown from "@components/common/MarkDown";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";

export default function BlogDetailsScreen() {
  const {
    params: { blog: blogId },
  } = useRoute<RouteProp<LearnNavParams, "BlogDetailsScreen">>();
  const { user } = useSession();

  const { data: blog, isLoading } = trpc.blog.getBlog.useQuery({
    blog: blogId,
  });

  useEffect(() => {
    (async () => {
      if(_.size(blog)) {
      await mixpanel.trackEvent(
        "Learn post details screen view",
        {
          post_name: blog?.title || "",  // need to re-verify row=>36
          post_id: blogId.toString() || "",
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    }
    })();
  }, [blog])

  const handleShareBlog = () => {
    Share.share({
      message: `${ENV.EXPO_PUBLIC_RABBLE_INV_BASE_URL}/learn/${String(blogId)}`,
    });
  };

  if (isLoading || !blog) return null;
  return (
    <Screen className="flex flex-1 mx-4">
      <ScrollView>
        <View style={{ gap: 4 }} className="flex">
          <Title classNames="text-2xl" value={blog.title} />
          <View className="flex-row items-center justify-between">
            <View className="mt-3 flex flex-row flex-start items-center">
              <AppText className="text-[16px] text-[#191D23] font-montserratMedium">
                by{" "}
                <AppText className="text-[16px] text-[#191D23] capitalize font-montserratSemiBold">
                  {blog.author}{" "}
                </AppText>
              </AppText>

              <AppText className="text-[14px] text-[#191D23] font-montserratRegular">
                {format(new Date(blog.publishDate), "MMM dd, yyyy")}
              </AppText>
            </View>

            <Pressable
              actionTag="share blog"
              className="m-0 p-0 flex justify-center items-center"
              onPress={handleShareBlog}
            >
              <Share2Icon size={18} color="#FF8E1C" />
            </Pressable>
          </View>
          <View className="mt-3">
            <Banner url={blog.banner} />
          </View>
          <View className="mt-3">
            <MarkDown content={blog.contentHtml} />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}
