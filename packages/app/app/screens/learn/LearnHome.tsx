import LoadingScreen from "@components/common/LoadingScreen";
import Screen from "@components/common/Screen";
import BlogList from "@components/learn/BlogList";
import Title from "@components/learn/Title";
import { useSession } from "@hooks/persistUser";
import { trpc } from "@providers/RootProvider";
import mixpanel from "@utils/mixpanel";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { Platform, ScrollView, View } from "react-native";
import { z } from "zod";
import { BlogStatus } from "../../../../shared/types/Blog";

// Schema for blog query parameters
export const blogQuerySchema = z.object({
  status: z.nativeEnum(BlogStatus),
  tags: z.array(z.string()), // Array of tag ObjectIds
});

export default function LearnHomeScreen() {
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const { user } = useSession();

  const blogDashboard = trpc.blog.getBlogDashboard.useQuery();

  const baseOptions = { status: BlogStatus.PUBLISHED };

  const blogs = trpc.blog.getBlogs.useQuery({
    ...baseOptions,
    tags: [...selectedTagIds],
  });

  useEffect(() => {
    blogs.refetch();
  }, [selectedTagIds]);

  useEffect(() => {
    (async () => {
      await mixpanel.trackEvent(
        "Learn screen view",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  if (blogDashboard.isLoading || blogs.isLoading) {
    return (
      <View className="flex-1">
        <LoadingScreen />
      </View>
    );
  }

  return (
    <Screen
      includeHeader
      headerClasses="mx-4"
      className={clsx("flex flex-1", Platform.OS === "android" && "pt-10")}
    >
      <ScrollView>
        <View className="flex">
          {/* {!!blogDashboard.data?.bannerImage && (
              <Banner url={blogDashboard.data?.bannerImage} />
            )} */}
          <Title value={blogDashboard.data?.title} classNames="mx-4" />

          <View className="flex flex-row justify-between mx-4">
            <View className="flex-1" />
          </View>

          <View className="mx-4">
            {/* Todo: Learn home page, show all blogs by default, add a filter */}
            <BlogList
              data={blogs.data}
              setFilterTags={setSelectedTagIds}
              filterTags={selectedTagIds}
            />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
}
