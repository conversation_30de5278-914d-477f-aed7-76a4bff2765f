import AppText from "@components/common/AppText";
import Tabs from "@components/common/Tabs";
import BottomSheet from "@components/common/BottomSheet";
import { useSession } from "@hooks/persistUser";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { Share } from "react-native";
import * as Linking from "expo-linking";
import { getTimeDifference, imageKit } from "@utils/index";
import React, { useMemo, useState, useEffect } from "react";
import { FlatList, Modal, ScrollView, View } from "react-native";
import { Image as CacheImage } from "react-native-expo-image-cache";
import SectionLink from "@components/profile/ProfileSectionItem";
import DeleteIcon from "@assets/svg/DeleteIcon";
import AdminIcon from "@assets/svg/AdminIcon";
import { trpc } from "@providers/RootProvider";
import LoadingScreen from "@components/common/LoadingScreen";
import { Globe2, Lock, LogOut, SettingsIcon } from "lucide-react-native";
import LogoSimple from "@assets/svg/LogoOutline";
import { StackNavigationProp } from "@react-navigation/stack";
import ConnectPosts from "@screens/connect/ConnectPosts";
import InfoModal from "@components/common/InfoModal";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { Entypo } from "@expo/vector-icons";
import InviteAndEditIcon from "@assets/svg/InviteAndEditIcon";
import { ENV } from "@constants/index";
import useAuthGuard from "@hooks/useAuthGuard";
import { alert } from "@providers/AlertProvider";
import Pressable from "@components/common/Pressable";
import { RabbleMember } from "@components/rabbleGroups/RabbleMember";
import { RabbleRequest } from "@components/rabbleGroups/RabbleRequest";
import mixpanel from "@utils/mixpanel";

const RabbleDetailsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const { user } = useSession();
  const { groupId, isCareGiverManagedGroup } =
    useRoute<RouteProp<RootNavParams, "RabbleDetailsScreen">>().params;

  const tabState = useState(0);
  const [tabIdx, setTabIdx] = tabState;
  const [deleteGroupOpen, setDeleteGroupOpen] = useState(false);
  const [userPromoted, setUserPromoted] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [groupDeleted, setGroupDeleted] = useState(false);
  const [deleteMsg, setDeleteMsg] = useState("");
  const rootNavigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const utils = trpc.useUtils();
  const [groupJoined, setGroupJoined] = useState(false);
  const [msg, setMsg] = useState("");

  const rabbleGroupInfo = trpc.rabbleGroups.getGroupInfo.useQuery({
    group: groupId,
  });

  const shareUrl = useMemo(
    () => `${ENV.EXPO_PUBLIC_RABBLE_INV_BASE_URL}/groups/${groupId}`,
    [groupId]
  );

  const { data: members, refetch: refetchMembers } =
    trpc.rabbleGroups.getUsersInGroup.useQuery({
      group: groupId,
    });

  const member = useMemo(
    () => members?.find((m: any) => m.user?._id === user?._id),
    [members, user]
  );
  const isOwner = member?.role === "owner";
  const isAdmin = member?.role === "admin";

  const joinPublicGroup = trpc.rabbleGroups.joinPublicRabbleGroup.useMutation();
  const requestJoinPvtGroup = trpc.rabbleGroups.requestInvite.useMutation();
  const leaveGroup = trpc.rabbleGroups.leaveGroup.useMutation();
  const groupNotifications = trpc.rabbleGroups.getRabbleInvites.useQuery(
    {
      rabbleGroupId: groupId,
    },
    { enabled: isAdmin || isOwner }
  );

  const authGuard = useAuthGuard();

  const handleJoinGroup = () =>
    authGuard(async () => {
      if (rabbleGroupInfo.data?.privacy === "public") {
        await joinPublicGroup.mutateAsync(
          {
            rabbleGroup: groupId,
            user: user?._id?.toString() as string,
          },
          {
            onSuccess() {
              setMsg("Joined group successfully");
              setGroupJoined(true);
              rabbleGroupInfo.refetch();
              refetchMembers();
              utils.rabbleGroups.invalidate();
            },
          }
        );
      } else {
        await requestJoinPvtGroup.mutateAsync(
          {
            rabbleGroup: groupId,
          },
          {
            onSuccess() {
              setSentInvitedModal(true);
              rabbleGroupInfo.refetch();
              refetchMembers();
              utils.rabbleGroups.invalidate();
            },
          }
        );
      }
      const groupUser =
        rabbleGroupInfo?.data?.createdBy &&
        (await getUserById.mutateAsync(
          String(rabbleGroupInfo?.data?.createdBy) || ""
        ));

      try {
        await mixpanel.trackEvent(
          "Rabbles group joined (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            group_id: groupId || "",
            group_status: !groupId
              ? "Inactive"
              : `Active`,
            group_admin: groupUser?.username || "",
            group_title: rabbleGroupInfo?.data?.groupName || "",
          },
          String(user?._id),
          "v2"
        );

        console.log("joined group");
      } catch (error) {
        console.log("Error to join group", error);
      }
    });

  const getUserById = trpc.user.getUserById.useMutation();

  useEffect(() => {
    (async () => {
      if (rabbleGroupInfo?.data) {
        const groupUser =
          rabbleGroupInfo?.data?.createdBy &&
          (await getUserById.mutateAsync(
            String(rabbleGroupInfo?.data?.createdBy) || ""
          ));

        await mixpanel.trackEvent(
          "Rabbles group page view(Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            group_id: groupId || "",
            group_status: !groupId
              ? "Inactive"
              : `Active`,
            group_admin: groupUser?.username || "",
            group_title: rabbleGroupInfo?.data?.groupName || "",
          },
          String(user?._id),
          "v2"
        );
      }
    })();
  }, [rabbleGroupInfo?.data]);

  const handleLeaveGroup = async () => {
    await leaveGroup.mutateAsync(
      { group: groupId },
      {
        onSuccess() {
          rootNavigation.goBack();
          setTabIdx(0);
        },
      }
    );
    let groupUser = null;

    if (rabbleGroupInfo?.data?.createdBy) {
      groupUser = await getUserById.mutateAsync(
        String(rabbleGroupInfo.data.createdBy)
      );
    }
    await mixpanel.trackEvent(
      "Rabble group left (Rabble screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        group_id: groupId || "",
        group_status: !groupId
          ? "Inactive"
          : `Active`,
        group_admin: groupUser?.username || "",
        group_title: rabbleGroupInfo?.data?.groupName || "",
      },
      String(user?._id),
      "v2"
    );
    console.log("grouop left");
  };

  const handleRabbleInvite = trpc.rabbleGroups.handleRabbleInvite.useMutation();

  const removeNotification = trpc.notification.removeNotification.useMutation();

  const handleNotification = async (
    accept: "accepted" | "rejected",
    requestedUser: string,
    requestId: string
  ) => {
    await handleRabbleInvite.mutateAsync(
      {
        rabbleGroup: groupId,
        requestedUser,
        requestId,
        requestStatus: accept,
      },
      {
        onSuccess: async () => {
          await removeNotification.mutateAsync({
            filters: {
              "metadata.groupId": groupId,
              requestedUserId: requestedUser,
            },
          });
          groupNotifications.refetch();
          rabbleGroupInfo.refetch();
          alert(accept + " request");
        },
      }
    );
  };

  const _promoteDemoteUser =
    trpc.rabbleGroups.promoteOrDemoteUser.useMutation();
  const _kickUser = trpc.rabbleGroups.kickUser.useMutation();

  const promoteDemoteUser = async (
    userId: string,
    roleToUpdate: "admin" | "user"
  ) => {
    await _promoteDemoteUser.mutateAsync(
      {
        rabbleGroupId: groupId,
        role: roleToUpdate,
        targetUserId: userId,
      },
      {
        onSuccess: () => {
          setErrorMsg(`user promoted/demoted to ${roleToUpdate}`);
          setUserPromoted(true);
          refetchMembers();
        },
      }
    );
  };
  const kickUser = async (userId: string) => {
    await _kickUser.mutateAsync(
      { group: groupId, userId },
      {
        onSuccess: () => {
          alert(`user kicked-out successfully`);
          refetchMembers();
        },
      }
    );
  };

  const deleteGroup = trpc.rabbleGroups.deleteRabbleGroup.useMutation();

  const handleDeleteGroup = () => {
    deleteGroup.mutateAsync(
      { groupId },
      {
        onSuccess: () => {
          setDeleteGroupOpen(false);
          rootNavigation.goBack();
          setDeleteMsg("group deleted successfully");
        },
      }
    );
  };

  // Modals
  const [leaveGroupModal, setLeaveGroupModal] = useState(false);
  const [sentInviteModal, setSentInvitedModal] = useState(false);
  const [descModal, setDescModal] = useState(false);

  const showRequests =
    rabbleGroupInfo.data?.privacy === "private" && (isAdmin || isOwner);

  const showMembers =
    rabbleGroupInfo.data?.privacy === "public" ||
    (rabbleGroupInfo.data?.privacy === "private" &&
      (isAdmin || isOwner || member));

  if (rabbleGroupInfo.isLoading) return <LoadingScreen />;
  return (
    <View className="flex flex-1 m-3">
      {/* Group desc modal */}
      <Modal visible={descModal} onRequestClose={() => setDescModal(false)}>
        <Screen className="flex-1 p-4">
          <Pressable
            className="flex-row items-center"
            onPress={() => setDescModal(false)}
          >
            <Entypo name="chevron-small-left" size={24} color="black" />
            <AppText className="text-lg font-montserratSemiBold ml-2">
              Group Description
            </AppText>
          </Pressable>
          <ScrollView>
            <AppText className="ml-8">
              {rabbleGroupInfo.data?.groupDescription}
            </AppText>
          </ScrollView>
        </Screen>
      </Modal>
      <View className="flex flex-row items-center gap-3">
        {/* Image */}
        {rabbleGroupInfo.data?.image && (
          <CacheImage
            style={{ width: 85, height: 85, borderRadius: 100 }}
            uri={imageKit({
              imagePath: rabbleGroupInfo.data?.image,
              transform: ["w-500"],
            })}
            preview={{
              uri: imageKit({
                imagePath: rabbleGroupInfo.data?.image,
                transform: ["w-500"],
              }),
            }}
          />
        )}

        <View className="flex-1">
          <AppText className="text-2xl font-montserratBold" numberOfLines={2}>
            {rabbleGroupInfo.data?.groupName}
          </AppText>
          <View className="flex-row items-center gap-1">
            {rabbleGroupInfo.data?.privacy === "private" ? (
              <Lock color="#acacac" size={16} />
            ) : (
              <Globe2 color="#acacac" size={16} />
            )}

            <AppText className="capitalize text-sm text-neutral-500">
              {rabbleGroupInfo.data?.privacy} group
            </AppText>
          </View>
        </View>
      </View>

      {/* Group Desc */}
      <Pressable onPress={() => setDescModal(true)}>
        <AppText numberOfLines={3} className="mt-2">
          {rabbleGroupInfo.data?.groupDescription}
        </AppText>
        <AppText className="text-accent font-montserratSemiBold self-end">
          Read More
        </AppText>
      </Pressable>

      {/* Tabs */}
      <Tabs
        tabState={tabState}
        tabs={[
          "Posts",
          showMembers ? "Members" : null,
          showRequests ? (
            <View className="px-2">
              <AppText>Requests</AppText>
              {!!groupNotifications.data?.length && (
                <AppText className=" text-white absolute w-6 h-6 text-center align-middle rounded-full -top-4 -right-4 bg-red-500">
                  {groupNotifications.data.length}
                </AppText>
              )}
            </View>
          ) : null,
          <SettingsIcon color="#023967" />,
        ]}
      />

      {/* Join Group */}
      <View className="flex-row mt-3">
        {!member && (
          <AppButton
            title={
              rabbleGroupInfo.data?.privacy === "public"
                ? isCareGiverManagedGroup
                  ? "Join group as caregiver"
                  : "Join Group"
                : isCareGiverManagedGroup
                ? "Request to join as caregiver"
                : "Request to join"
            }
            onPress={handleJoinGroup}
          />
        )}
      </View>

      <View className="mt-4 flex-1">
        {tabIdx === 0 && (member || isCareGiverManagedGroup) && (
          <ConnectPosts
            // @ts-ignore
            postType={["PRIVATE", "PUBLIC"]}
            rabbleGroupId={groupId}
          />
        )}
        {tabIdx === 1 && showMembers && (
          <FlatList
            data={members}
            keyExtractor={(item) => item._id.toString()}
            renderItem={({
              item: { _id, rabbleGroup, role, user: rabbleUser },
            }) => {
              const rabbleMember = members?.find(
                (m: any) => m.user._id === rabbleUser._id
              );
              return (
                <RabbleMember
                  fullname={`${rabbleUser.firstname} ${rabbleUser.lastname}`}
                  username={rabbleUser.username as string}
                  isMember={!!member}
                  isAdmin={
                    rabbleMember?.role === "admin" ||
                    rabbleMember?.role === "owner"
                  }
                  profilePicture={rabbleMember?.user.profilePicture}
                  promoteDemoteUser={promoteDemoteUser}
                  kickUser={kickUser}
                  role={rabbleMember?.role as string}
                  userId={rabbleUser._id.toString()}
                  loggedInUserId={user?._id?.toString() as string}
                  loggedInUserMemberRole={member?.role as string}
                />
              );
            }}
          />
        )}

        {tabIdx === 2 && (
          <FlatList
            data={groupNotifications.data}
            keyExtractor={(item) => item._id.toString()}
            renderItem={({
              item: {
                _id: requestId,
                requestedBy: {
                  firstname,
                  lastname,
                  username,
                  profilePicture,
                  _id,
                },
                // @ts-ignore
                createdAt,
              },
            }) => {
              return (
                <RabbleRequest
                  createdAt={createdAt}
                  fullname={`${firstname} ${lastname}`}
                  username={username as string}
                  profilePicture={profilePicture}
                  onPress={(d) =>
                    handleNotification(d, _id.toString(), requestId.toString())
                  }
                />
              );
            }}
          />
        )}
      </View>
      {/* Leave Group Modal */}
      <InfoModal
        visible={leaveGroupModal}
        showLogo
        label={`Leave ${rabbleGroupInfo.data?.groupName}`}
        description="Are you sure you want to Leave this group? This action cannot be undone."
        secondaryText="No"
        handleSecondaryAction={() => setLeaveGroupModal(false)}
        handleAction={handleLeaveGroup}
      />
      <BottomSheet
        visible={tabIdx === (showRequests ? 3 : showMembers ? 2 : 1)}
        handler={() => setTabIdx(0)}
      >
        <AppText>group setting</AppText>
        <SectionLink
          title="Invite people"
          showRightArrow={false}
          icon={<InviteAndEditIcon />}
          onPress={() => {
            setTabIdx(0);
            Share.share({
              message: `I want to add you to a group ${rabbleGroupInfo.data?.groupName}. To join click on the link ${shareUrl}`,
            });
          }}
        />

        {(isOwner || isAdmin) && (
          <SectionLink
            title="Edit Group"
            showRightArrow={false}
            icon={<InviteAndEditIcon />}
            onPress={() => {
              setTabIdx(0);
              navigation.navigate(
                "CreateOrEditRabbleGroupScreen",
                rabbleGroupInfo.data
                  ? {
                      rabbleGroupInfo: rabbleGroupInfo?.data,
                      groupId,
                      diseaseTags: rabbleGroupInfo?.data?.diseaseTags,
                      tags: rabbleGroupInfo?.data?.tags,
                    }
                  : {}
              );
            }}
          />
        )}
        <View>
          {isOwner && member && (
            <SectionLink
              title="Delete Group"
              showRightArrow={false}
              icon={<DeleteIcon />}
              onPress={() => {
                // setTabIdx(0);
                setDeleteGroupOpen(true);
              }}
            />
          )}
          {!isOwner && member && (
            <SectionLink
              title="Leave Group"
              icon={<LogOut color="#023967" />}
              onPress={() => {
                // setTabIdx(0);
                setLeaveGroupModal(true);
              }}
            />
          )}
        </View>
      </BottomSheet>

      <InfoModal
        secondaryText="No"
        actionText="Yes"
        showLogo
        label={`Delete ${rabbleGroupInfo.data?.groupName}`}
        description="Are you sure you want to delete this group? This action cannot be undone. All data will be lost"
        visible={deleteGroupOpen}
        handleSecondaryAction={() => {
          setDeleteGroupOpen(false);
          setTabIdx(0);
        }}
        handleAction={handleDeleteGroup}
      />
      <InfoModal
        visible={sentInviteModal}
        showLogo
        label={"Request sent Successfully."}
        description="Members may join the group after they accept the invitation."
        handleSecondaryAction={() => setSentInvitedModal(false)}
        handleAction={() => setSentInvitedModal(false)}
      />
      {/* Modal for Group Creation*/}
      <InfoModal
        visible={userPromoted}
        showLogo
        label={errorMsg}
        handleSecondaryAction={() => setUserPromoted(false)}
        handleAction={() => setUserPromoted(false)}
      />
      <InfoModal
        visible={groupJoined}
        showLogo
        label={msg}
        handleSecondaryAction={() => setGroupJoined(false)}
        handleAction={() => setGroupJoined(false)}
      />
      <InfoModal
        visible={groupDeleted}
        showLogo
        label={deleteMsg}
        handleSecondaryAction={() => setGroupDeleted(false)}
        handleAction={() => setGroupDeleted(false)}
      />
    </View>
  );
};

export default RabbleDetailsScreen;
