import AppText from "@components/common/AppText";
import Tabs from "@components/common/Tabs";
import BottomSheet from "@components/common/BottomSheet";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import {
  ActivityIndicator,
  Image,
  KeyboardAvoidingView,
  Platform,
  Share,
  Text,
  TextInput,
} from "react-native";
import * as Linking from "expo-linking";
import { getTimeDifference, imageKit } from "@utils/index";
import React, { useMemo, useState, useEffect, useRef } from "react";
import { FlatList, Modal, ScrollView, View } from "react-native";
import { Image as CacheImage } from "react-native-expo-image-cache";
import SectionLink from "@components/profile/ProfileSectionItem";
import DeleteIcon from "@assets/svg/DeleteIcon";
import AdminIcon from "@assets/svg/AdminIcon";
import { trpc } from "@providers/RootProvider";
import * as ImagePicker from "expo-image-picker";
import LoadingScreen from "@components/common/LoadingScreen";
import {
  ChevronLeft,
  Globe2,
  Lock,
  LogOut,
  SettingsIcon,
} from "lucide-react-native";
import LogoSimple from "@assets/svg/LogoOutline";
import { StackNavigationProp } from "@react-navigation/stack";
import ConnectPosts from "@screens/connect/ConnectPosts";
import InfoModal from "@components/common/InfoModal";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { Entypo } from "@expo/vector-icons";
import InviteAndEditIcon from "@assets/svg/InviteAndEditIcon";
import { ENV, s3Paths } from "@constants/index";
import useAuthGuard from "@hooks/useAuthGuard";
import { alert } from "@providers/AlertProvider";
import Pressable from "@components/common/Pressable";
import { RabbleMember } from "@components/rabbleGroups/RabbleMember";
import { RabbleRequest } from "@components/rabbleGroups/RabbleRequest";
import mixpanel from "@utils/mixpanel";
import {
  Camera,
  DefaultIcon,
  Plus,
  Speak,
} from "./PostOrGroupPostDetailsScreen";
import _ from "lodash";
import AppTextInput from "@components/common/AppTextInput";
import { useUploadImage } from "@services/upload";
import Toast from "react-native-toast-message";
import errorHandler from "@utils/errorhandler";
import { Dimensions } from "react-native";
import Rocket from "@assets/svg/connect/Rocket";
import { Alert } from "react-native";
import useScreenTracking from "@hooks/useScreenTracking";

const { width } = Dimensions.get("screen");
const MARGIN = 16;
const postImageWidth = width - 2 * MARGIN;
const postImageHeight = (postImageWidth / 4) * 3;

const RabbleDetailsScreenV2 = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const { user } = useSession();
  const {
    groupId,
    isCareGiverManagedGroup,
    isAutoGroupCreated = false,
  } = useRoute<RouteProp<RootNavParams, "RabbleDetailsScreenV2">>().params;

  const tabState = useState(0);
  const [tabIdx, setTabIdx] = tabState;
  const [deleteGroupOpen, setDeleteGroupOpen] = useState(false);
  const [userPromoted, setUserPromoted] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [groupDeleted, setGroupDeleted] = useState(false);
  const [deleteMsg, setDeleteMsg] = useState("");
  const rootNavigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const utils = trpc.useUtils();
  const [groupJoined, setGroupJoined] = useState(false);
  const [msg, setMsg] = useState("");

  const { setOnboardingProgressScreen } = useScreenTracking();
  const { persistedUser, setPersistedUser } = usePersistedUser();

  const rabbleGroupInfo = trpc.rabbleGroups.getGroupInfo.useQuery({
    group: groupId,
  });

  const shareUrl = useMemo(
    () => `${ENV.EXPO_PUBLIC_RABBLE_INV_BASE_URL}/groups/${groupId}`,
    [groupId]
  );

  const { data: members, refetch: refetchMembers } =
    trpc.rabbleGroups.getUsersInGroup.useQuery({
      group: groupId,
    });

  const member = useMemo(
    () => members?.find((m: any) => m.user?._id === user?._id),
    [members, user]
  );
  const isOwner = member?.role === "owner";
  const isAdmin = member?.role === "admin";

  const joinPublicGroup = trpc.rabbleGroups.joinPublicRabbleGroup.useMutation();
  const requestJoinPvtGroup = trpc.rabbleGroups.requestInvite.useMutation();
  const leaveGroup = trpc.rabbleGroups.leaveGroup.useMutation();
  const groupNotifications = trpc.rabbleGroups.getRabbleInvites.useQuery(
    {
      rabbleGroupId: groupId,
    },
    { enabled: isAdmin || isOwner }
  );

  const authGuard = useAuthGuard();

  const handleJoinGroup = () =>
    authGuard(async () => {
      if (rabbleGroupInfo.data?.privacy === "public") {
        await joinPublicGroup.mutateAsync(
          {
            rabbleGroup: groupId,
            user: user?._id?.toString() as string,
          },
          {
            onSuccess() {
              setMsg("Joined group successfully");
              setGroupJoined(true);
              rabbleGroupInfo.refetch();
              refetchMembers();
              utils.rabbleGroups.invalidate();
            },
          }
        );
      } else {
        await requestJoinPvtGroup.mutateAsync(
          {
            rabbleGroup: groupId,
          },
          {
            onSuccess() {
              setSentInvitedModal(true);
              rabbleGroupInfo.refetch();
              refetchMembers();
              utils.rabbleGroups.invalidate();
            },
          }
        );
      }
      const groupUser =
        rabbleGroupInfo?.data?.createdBy &&
        (await getUserById.mutateAsync(
          String(rabbleGroupInfo?.data?.createdBy) || ""
        ));

      try {
        await mixpanel.trackEvent(
          "Rabbles group joined (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            group_id: groupId || "",
            group_status: !groupId ? "Inactive" : `Active`,
            group_admin: groupUser?.username || "",
            group_title: rabbleGroupInfo?.data?.groupName || "",
          },
          String(user?._id),
          "v2"
        );

        console.log("joined group");
      } catch (error) {
        console.log("Error to join group", error);
      }
    });

  const getUserById = trpc.user.getUserById.useMutation();

  useEffect(() => {
    (async () => {
      if (rabbleGroupInfo?.data) {
        const groupUser =
          rabbleGroupInfo?.data?.createdBy &&
          (await getUserById.mutateAsync(
            String(rabbleGroupInfo?.data?.createdBy) || ""
          ));

        await mixpanel.trackEvent(
          "Rabbles group page view(Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            group_id: groupId || "",
            group_status: !groupId ? "Inactive" : `Active`,
            group_admin: groupUser?.username || "",
            group_title: rabbleGroupInfo?.data?.groupName || "",
          },
          String(user?._id),
          "v2"
        );
      }
    })();
  }, [rabbleGroupInfo?.data]);

  const handleLeaveGroup = async () => {
    await leaveGroup.mutateAsync(
      { group: groupId },
      {
        onSuccess() {
          rootNavigation.goBack();
          setTabIdx(0);
        },
      }
    );
    let groupUser = null;

    if (rabbleGroupInfo?.data?.createdBy) {
      groupUser = await getUserById.mutateAsync(
        String(rabbleGroupInfo.data.createdBy)
      );
    }
    await mixpanel.trackEvent(
      "Rabble group left (Rabble screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        group_id: groupId || "",
        group_status: !groupId ? "Inactive" : `Active`,
        group_admin: groupUser?.username || "",
        group_title: rabbleGroupInfo?.data?.groupName || "",
      },
      String(user?._id),
      "v2"
    );
    console.log("grouop left");
  };

  const handleRabbleInvite = trpc.rabbleGroups.handleRabbleInvite.useMutation();

  const removeNotification = trpc.notification.removeNotification.useMutation();

  const handleNotification = async (
    accept: "accepted" | "rejected",
    requestedUser: string,
    requestId: string
  ) => {
    await handleRabbleInvite.mutateAsync(
      {
        rabbleGroup: groupId,
        requestedUser,
        requestId,
        requestStatus: accept,
      },
      {
        onSuccess: async () => {
          await removeNotification.mutateAsync({
            filters: {
              "metadata.groupId": groupId,
              requestedUserId: requestedUser,
            },
          });
          groupNotifications.refetch();
          rabbleGroupInfo.refetch();
          alert(accept + " request");
        },
      }
    );
  };

  const _promoteDemoteUser =
    trpc.rabbleGroups.promoteOrDemoteUser.useMutation();
  const _kickUser = trpc.rabbleGroups.kickUser.useMutation();

  const promoteDemoteUser = async (
    userId: string,
    roleToUpdate: "admin" | "user"
  ) => {
    await _promoteDemoteUser.mutateAsync(
      {
        rabbleGroupId: groupId,
        role: roleToUpdate,
        targetUserId: userId,
      },
      {
        onSuccess: () => {
          setErrorMsg(`user promoted/demoted to ${roleToUpdate}`);
          setUserPromoted(true);
          refetchMembers();
        },
      }
    );
  };
  const kickUser = async (userId: string) => {
    await _kickUser.mutateAsync(
      { group: groupId, userId },
      {
        onSuccess: () => {
          alert(`user kicked-out successfully`);
          refetchMembers();
        },
      }
    );
  };

  const deleteGroup = trpc.rabbleGroups.deleteRabbleGroup.useMutation();

  const handleDeleteGroup = () => {
    deleteGroup.mutateAsync(
      { groupId },
      {
        onSuccess: () => {
          setDeleteGroupOpen(false);
          rootNavigation.goBack();
          setDeleteMsg("group deleted successfully");
        },
      }
    );
  };
  const postType = "PRIVATE";
  // const handleCreatePostNew = () => {
  //   authGuard(() => {
  //     console.log({ groupId });
  //     if (postType) {
  //       rootNavigation.navigate("CreatePostScreen", {
  //         postType,
  //         rabbleGroup: groupId,
  //       });
  //     }
  //   });
  // };

  // Modals
  const [leaveGroupModal, setLeaveGroupModal] = useState(false);
  const [sentInviteModal, setSentInvitedModal] = useState(false);
  const [descModal, setDescModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<
    ImagePicker.ImagePickerAsset | undefined
  >(undefined);
  const [caption, setCaption] = useState("");
  const uploadImage = useUploadImage();

  const captionRef = useRef<TextInput>(null);

  const handleCaptionInputFocus = () => {
    if (captionRef.current) captionRef.current.focus();
  };

  const handleSelectImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled) {
      setSelectedImage(result.assets[0]);
    }
  };

  const createPost = trpc.connect.createPost.useMutation();
  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery({
    group: groupId,
  });
  const { data: groupUser } = trpc.user.me.useQuery(
    String(groupInfo?.data?.createdBy),
    { enable: !!groupInfo?.data }
  );

  const handleCreatePost = async () => {
    try {
      // Disable the button if already loading or no image/caption selected
      let imageShared = "";
      if (
        createPost.isLoading ||
        uploadImage.isLoading ||
        !(selectedImage || caption)
      )
        return;

      const formdata = new FormData();
      if (selectedImage?.uri) {
        formdata.append("filePath", s3Paths.post);
        formdata.append("image", {
          uri: selectedImage?.uri,
          type: "image/png",
          name: "image.png",
        } as any);

        const {
          data: { s3Path },
        } = await uploadImage.mutateAsync({ formdata });
        imageShared = s3Path;
        const createdPost = await createPost.mutateAsync({
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup: groupId,
          title: caption,
          image: s3Path,
        });
        await mixpanel.trackEvent(
          "Post created (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            post_content: caption,
            post_id: createdPost?._id || "", // need confirm
            images_shared: imageShared || "",
            time: new Date().toISOString(),
            group_title: groupInfo?.data?.groupName || "",
            group_status: "Inactive",
            group_admin:
              `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` ||
              "",
            group_id: groupId || "",
          },
          String(user?._id),
          "v2"
        );
      } else {
        const createdPost = await createPost.mutateAsync({
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup: groupId,
          title: caption,
        });
        await mixpanel.trackEvent(
          "Post created (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            post_content: caption,
            post_id: createdPost?._id || "", // need confirm
            images_shared: imageShared || "",
            time: new Date().toISOString(),
            group_title: groupInfo?.data?.groupName || "",
            group_status: !groupId ? "Inactive" : `Active`,
            group_admin:
              `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` ||
              "",
            group_id: groupId || "",
          },
          String(user?._id),
          "v2"
        );
      }

      setCaption("");
      setSelectedImage(undefined);

      // Show success toast notification
      Toast.show({
        type: "success",
        text1: "post created successfully",
        visibilityTime: 2000,
      });

      // Invalidate
      // utils.connect.getPosts.invalidate();
      navigation.goBack();
    } catch (ex) {
      // Handle errors
      const err = errorHandler(ex);
      alert(err ? err : "Something went wrong");
    }
  };

  const showRequests =
    rabbleGroupInfo.data?.privacy === "private" && (isAdmin || isOwner);

  const showMembers =
    rabbleGroupInfo.data?.privacy === "public" ||
    (rabbleGroupInfo.data?.privacy === "private" &&
      (isAdmin || isOwner || member));

  if (rabbleGroupInfo.isLoading) return <LoadingScreen />;
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <Screen className="flex-1 bg-[#F5F7F9]">
        <View className="flex-1">
          <View className="flex-1">
            {/* Header */}
            <View className="flex-row items-center mt-8 px-4">
              <ChevronLeft
                color="#0B79D3"
                size={22}
                onPress={async () => {
                  if(isAutoGroupCreated){
                    navigation.navigate('RabbleGroups', { selectedTab: 1 });
                    return;
                  }
                  navigation.goBack();
                }}
                className="mr-4"
              />
              {rabbleGroupInfo.data?.image ? (
                <CacheImage
                  style={{ width: 40, height: 41, borderRadius: 10 }}
                  uri={imageKit({
                    imagePath: rabbleGroupInfo.data?.image,
                    transform: ["w-500"],
                  })}
                  preview={{
                    uri: imageKit({
                      imagePath: rabbleGroupInfo.data?.image,
                      transform: ["w-500"],
                    }),
                  }}
                />
              ) : (
                <View className="ml-4">
                  <DefaultIcon />
                </View>
              )}
              <View className="ml-3">
                <Text className="text-[#0B79D3] font-[Montserrat] text-[14px] font-[600]">
                  {rabbleGroupInfo.data?.groupName || "Fresh Air Forum"}
                </Text>
                <Text className="text-[#336D9F] font-[Montserrat] text-[10px] font-[500]">
                  Tap here for more info...
                </Text>
              </View>
            </View>

            {/* Chat messages */}
            <ScrollView
              contentContainerStyle={{ padding: 16, rowGap: 24 }}
              showsVerticalScrollIndicator={false}
            >
              <ConnectPosts
                postType={["PRIVATE", "PUBLIC"]}
                rabbleGroupId={groupId}
                isAutoGroupCreated={isAutoGroupCreated}
              />
              {!member && (
                <AppButton
                  title={
                    rabbleGroupInfo.data?.privacy === "public"
                      ? isCareGiverManagedGroup
                        ? "Join group as caregiver"
                        : "Join Group"
                      : isCareGiverManagedGroup
                      ? "Request to join as caregiver"
                      : "Request to join"
                  }
                  onPress={handleJoinGroup}
                />
              )}
              <View className="flex flex-1">
                {/* <AppText onPress={handleCaptionInputFocus} className="mt-4">
                  {caption}
                </AppText> */}
                {selectedImage && (
                  <Image
                    source={{ uri: selectedImage.uri }}
                    resizeMode="cover"
                    className="mt-4 rounded-lg"
                    style={{ width: postImageWidth, height: postImageHeight }}
                  />
                )}
              </View>
            </ScrollView>
          </View>
          {member && (
            <View className="flex-row items-center justify-between px-4 py-3">
              {/* <Plus /> */}
              <Pressable className="flex-1 px-2">
                <AppTextInput
                  // placeholder="Share something here..."
                  borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF] bg-white"
                  placeholderTextColor="rgba(0, 73, 135, 0.40)"
                  className="h-12 text-[18px] w-full"
                  wrapperClass={`${caption ? "mr-0" : "mr-2"}`}
                  multiline
                  onChangeText={setCaption}
                  value={caption}
                  //   onPress={handleCreatePost}
                />
              </Pressable>
              {caption && (
                <Pressable
                  actionTag="create post"
                  onPress={handleCreatePost}
                  className="bg-primary w-[50px] h-[50px] justify-center items-center rounded-lg ml-1"
                  //   disabled={createPost?.status === "loading"}
                  style={{ backgroundColor: caption ? "#004987" : "#ccc" }}
                >
                  {createPost?.isLoading ? (
                    <ActivityIndicator color="#fff" />
                  ) : (
                    <Rocket />
                  )}
                </Pressable>
              )}
              {!caption && (
                <Camera onPress={handleSelectImage} className="mr-2" />
              )}
              {!caption && (
                <Speak
                  onPress={() => Alert.alert("Coming Soon")}
                  className="ml-2"
                />
              )}
            </View>
          )}
        </View>
      </Screen>
    </KeyboardAvoidingView>
  );
};

export default RabbleDetailsScreenV2;
