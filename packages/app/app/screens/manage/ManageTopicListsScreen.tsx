import AppText from "@components/common/AppText";
import {
  ManageNavParams,
  ManageTopicNavParams,
} from "@navigation/manage-navigator/ManageNavParams";
import { trpc } from "@providers/RootProvider";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { ChevronRight, EditIcon } from "lucide-react-native";
import { FlatList, Pressable, View } from "react-native";
import Screen from "../../components/common/Screen";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import _ from "lodash";
import { useMemo } from "react";

export default function ManageTopicListsScreen() {
  const topics = trpc.manage.getTopics.useQuery({ status: "Published" });
  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      enabled: false,
    }
  );
  const userSubscribedTopicIds = useMemo(
    () => _.map(userTopics.data, (d) => d.topic._id),
    [userTopics]
  );
  // console.log({ topics: topics.data, userTopics: userSubscribedTopicIds });
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();

  const onTopicSelect = (topicId: string) => {
    navigation.navigate("TopicQuestionsList", { topicId });
  };

  return (
    <Screen>
      <View className="flex-1 p-4 bg-white">
        {/* Header */}
        <View>
          <Pressable
            className="flex-row justify-end"
            onPress={() => navigation.goBack()}
          >
            <AppText className="font-montserratMedium text-base text-primary">
              Cancel
            </AppText>
          </Pressable>
          <AppText className="text-3xl text-primary font-montserratSemiBold">
            Select Topic
          </AppText>
        </View>

        {/* Search */}
        {/* <View className="flex"></View> */}

        <View className="flex-1 flex-col ">
          <FlatList
            data={topics.data ?? []}
            keyExtractor={(item) => String(item._id)}
            renderItem={({ item }) => {
              return (
                <Pressable
                  className="flex-row items-center justify-between px py-5 border-b border-[#bec0c2]"
                  onPress={() => onTopicSelect(String(item._id))}
                >
                  <AppText className="font-montserratMedium text-base">
                    {item.name}
                  </AppText>
                  {_.includes(userSubscribedTopicIds, item._id) ? (
                    <EditIcon color="#bdbfc2" size={20} />
                  ) : (
                    <ChevronRight color="#bdbfc2" size={20} />
                  )}
                </Pressable>
              );
            }}
          />
        </View>
      </View>
    </Screen>
  );
}
