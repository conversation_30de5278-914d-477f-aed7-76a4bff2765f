import AppButton from "@components/common/AppButton";
import AppText from "@components/common/AppText";
import LoadingScreen from "@components/common/LoadingScreen";
import Screen from "@components/common/Screen";
import ManageHeader from "@components/manage/ManageHeader";
import ManageTopicSubscriptionQuestion from "@components/manage/ManageTopicSubscriptionQuestion";
import { zodResolver } from "@hookform/resolvers/zod";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { useForm } from "react-hook-form";
import { Platform, ScrollView, View } from "react-native";
import { z } from "zod";
import { upsertUserTopicValidator } from "../../../../shared/validators/manage.validator";
import { StackNavigationProp } from "@react-navigation/stack";
import { useCallback, useEffect, useMemo, useState } from "react";
import clsx from "clsx";
import { QuestionTypes } from "../../../../shared/types/manage";
import AsyncStorage from "@react-native-async-storage/async-storage";

type Form = z.infer<typeof upsertUserTopicValidator>;

const carePartnerAnswerIds = [
  "672b917533d5cc5cc4a73360",
  "672b8f3ef71d437737e100c2",
  "672b8fb3d22680bded087b89",
];

interface OptionSelectProps {
  optionId: string;
  answerText: string;
  questionId: string;
}

export default function ManageTopicQuestionsList() {
  const {
    params: { topicId },
  } = useRoute<RouteProp<ManageNavParams, "TopicQuestionsNavigator">>();
  const methods = useForm<Form>({
    resolver: zodResolver(upsertUserTopicValidator),
    defaultValues: { questionAnswers: [], topic: topicId },
  });

  const topics = trpc.manage.getTopics.useQuery(
    { topicIds: [topicId] },
    {
      onSuccess(data) {
        if (!data.length) return;
        const [topic] = data;
        methods.setValue(
          "questionAnswers",
          topic.questions.map((q) => ({
            question: String(q._id),
            selectedOption: [],
            answerText: "",
          }))
        );
      },
    }
  );

  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      enabled: false,
      onSuccess(data) {
        const formdata = data
          .find((t) => t.topic._id === topicId)
          ?.questionAnswers?.map((q) => ({
            question: String(q.question),
            selectedOption: q.selectedOption,
            answerText: q.answerText,
          }));

        // @ts-expect-error
        if (formdata?.length) methods.setValue("questionAnswers", formdata);
      },
    }
  );

  const userSubscribedTopicIds = useMemo(
    () => _.map(userTopics.data, (d) => d.topic._id),
    [userTopics]
  );

  useFocusEffect(
    useCallback(() => {
      if (topics.status === "success") {
        userTopics.refetch();
      }
    }, [topics.status])
  );

  const subscripeTopic = trpc.manage.upsertUserTopic.useMutation();
  const unsubscribeTopic = trpc.manage.unsubscribeUserTopic.useMutation();

  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const formdata = methods.watch();

  const [canSubmitForm, setCanSubmitForm] = useState(false);

  useEffect(() => {
    const requiredQuestions: Record<string, true> = {};
    topics.data?.forEach((t) =>
      t.questions.forEach((q) => {
        if (!q.isOptional) {
          requiredQuestions[String(q._id)] = true;
        }
      })
    );

    const canSubmit = formdata.questionAnswers?.every((qa) => {
      const question = qa.question;
      if (!requiredQuestions[question]) return true;
      if (qa.answerText?.trim() && qa.selectedOption?.length > 0) return true;
      return false;
    });

    setCanSubmitForm(canSubmit);
  }, [topics.data, formdata]);

  if (topics.isLoading) return <LoadingScreen />;
  if (!topics.data?.length) return null;

  const [topic] = topics.data;

  return (
    <Screen
      className={clsx(
        "flex flex-1 bg-[#f5f7f9]",
        Platform.OS === "android" && "pt-10"
      )}
    >
      <View className="flex flex-1">
        <View className="mx-4 mt-4 overflow-visible" style={{ gap: 10 }}>
          <ManageHeader
            onCancelPress={() => navigation.navigate("ManageHomeScreen")}
          />
          <AppText className="text-3xl text-primary font-montserratSemiBold">
            {topic.name}
          </AppText>
        </View>

        {/* Questions */}
        <ScrollView
          contentContainerStyle={{
            gap: 16,
            marginTop: 16,
            paddingHorizontal: 16,
          }}
        >
          {/* Alert */}
          <View className="p-4 bg-foundation-light rounded-2xl">
            <AppText className="text-primary text-sm ">
              To subscribe to this topic, please answer the following questions.
              Your answers will remain private to other users unless you
              explicitly grant permission to share them.
            </AppText>
          </View>
          {topic.questions.map((q, idx) => {
            const selectedAnswer = methods.watch(
              `questionAnswers.${idx}.answerText`
            );
            const selectedOptions = methods.watch(
              `questionAnswers.${idx}.selectedOption`
            );

            const handleOptionSelect = ({
              optionId,
              answerText,
              questionId,
            }: OptionSelectProps) => {
              const currentOptionSelected =
                (methods.getValues(`questionAnswers.${idx}.selectedOption`) as
                  | string[]
                  | undefined) ?? [];

              let updatedSelection: string[] = [];

              if (q.type === QuestionTypes.MultiSelect) {
                updatedSelection = currentOptionSelected?.includes(optionId)
                  ? currentOptionSelected.filter((id) => id !== optionId)
                  : [...(currentOptionSelected || []), optionId];
              } else {
                updatedSelection =
                  currentOptionSelected?.[0] === optionId ? [] : [optionId];
              }

              // Update form values
              methods.setValue(
                `questionAnswers.${idx}.selectedOption`,
                updatedSelection
              );
              methods.setValue(`questionAnswers.${idx}.question`, questionId);
              methods.setValue(`questionAnswers.${idx}.answerText`, answerText);
            };

            return (
              <ManageTopicSubscriptionQuestion
                questionIndex={idx + 1}
                question={q.question}
                questionType={q.type}
                questionId={String(q._id)}
                optional={q.isOptional}
                options={(q.options ?? []).map((o) => ({
                  option: o.value,
                  active: Array.isArray(selectedOptions)
                    ? selectedOptions.includes(String(o._id))
                    : false,
                  optionId: String(o._id),
                }))}
                onOptionSelect={handleOptionSelect}
              />
            );
          })}

          <View className="mb-8">
            <View className="flex flex-row mt-[-16px]">
              <AppButton
                className={clsx("mt-4", canSubmitForm ? "" : "bg-[#ABC3D7]")}
                disabled={!canSubmitForm}
                variant={canSubmitForm ? "primary" : "disabled"}
                title="Subscribe"
                onPress={methods.handleSubmit(async (data) => {
                  try {
                    const isCarePartner = _.some(
                      data.questionAnswers,
                      (qa) =>
                        _.intersection(
                          carePartnerAnswerIds,
                          qa.selectedOption || []
                        ).length > 0
                    );
                    await subscripeTopic.mutateAsync({
                      ...data,
                      isCarePartner,
                      questionAnswers: data.questionAnswers.filter(
                        (q) => q.answerText
                      ),
                    });
                    navigation.navigate("ManageHomeScreen");
                  } catch (ex) {
                    if (ex instanceof Error) alert(ex.message);
                  }
                })}
              />
            </View>
            {_.includes(userSubscribedTopicIds, topic._id) && userSubscribedTopicIds.length > 1 && (
              <View className="flex flex-row mt-4">
                <AppButton
                  textClassName="text-red-500"
                  title="Unsubscribe"
                  size="large"
                  variant="outline"
                  onPress={async () => {
                    const selected = await AsyncStorage.getItem('selectedTopic');
                    if (selected === topic._id.toString()) {
                      await AsyncStorage.removeItem('selectedTopic');
                    }
                    unsubscribeTopic
                      .mutateAsync({ topic: topic._id.toString() })
                      .then(() => {
                        navigation.navigate("ManageHomeScreen");
                      });
                  }}
                />
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </Screen>
  );
}
