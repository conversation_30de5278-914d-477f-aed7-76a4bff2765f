import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";
import LearnHomeScreen from "@screens/learn/LearnHome";
import BlogDetailsScreen from "@screens/learn/BlogDetailsScreen";

import { LearnNavParams } from "./LearnNavParams";

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const Stack = createStackNavigator<LearnNavParams>();

export default function LearnNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{ ...options }}
      initialRouteName={"LearnHomeScreen"}
    >
      <Stack.Screen
        name="LearnHomeScreen"
        component={LearnHomeScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="BlogDetailsScreen"
        component={BlogDetailsScreen}
        options={{
          headerShown: true,
          headerTitle: "Learn",
        }}
      />
    </Stack.Navigator>
  );
}
