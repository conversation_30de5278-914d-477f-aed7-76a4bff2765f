import _ from "lodash";
import { createContext, useContext, useState } from "react";
import { trpc } from "./RootProvider";
import { Topic } from "../../../shared/types/manage";

const AppContext = createContext({
  notficationCount: 0,
  refetchNotificationCount: _.noop,
  topics: {},
  setTopics: ({}) => {},
});

type Props = {
  children: any;
};

export default function AppProvider({ children }: Props) {
  const { data: notficationCount, refetch: refetchNotificationCount } =
    trpc.notification.getNotificationCount.useQuery();
  const [topics, setTopics] = useState<any>({});

  return (
    <AppContext.Provider
      value={{
        notficationCount: notficationCount || 0,
        refetchNotificationCount,
        topics,
        setTopics,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppProvider() {
  const { notficationCount, refetchNotificationCount, topics, setTopics } = useContext(AppContext);
  return { notficationCount, refetchNotificationCount, topics, setTopics };
}
