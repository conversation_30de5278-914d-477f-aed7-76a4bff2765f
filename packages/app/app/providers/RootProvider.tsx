import { ENV } from "@constants/index";
import { AppInstanceIdProvider } from "@hooks/useAppInstanceId";
import FontsProvider from "@providers/FontsProvider";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  createTRPCProxyClient,
  createTRPCReact,
  httpBatchLink,
} from "@trpc/react-query";
import AsyncStorage from "@utils/asyncStorage";
import { StatusBar } from "expo-status-bar";
import type { AppRouter } from "rabble_be/app";
import { createContext, useContext, useState } from "react";
import { Platform } from "react-native";
import Toast from "react-native-toast-message";
import SuperJSON from "superjson";
import type { JwtUser } from "../../../shared/types/user";
import AlertProvider from "./AlertProvider";
import AppProvider from "./AppProvider";
import Tracker from "./Tracker";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      onError: console.log,
      retry: false,
    },
  },
});

export const trpc = createTRPCReact<AppRouter>();

const localhost =
  process.env.EXPO_PUBLIC_IPV4_ADDRESS ||
  (Platform.OS === "android" ? "********" : "127.0.0.1");
const localServer = `http://${localhost}:8080/api/v1/trpc`;

const trpcUrl = __DEV__ ? localServer : `${ENV.EXPO_PUBLIC_API_URL}/trpc`;
console.log(trpcUrl);
// const trpcUrl = "https://dev-connect.getrabble.co/api/v1/trpc";

// trpc client
const trpcClient = trpc.createClient({
  transformer: SuperJSON,
  links: [
    httpBatchLink({
      url: trpcUrl,
      headers: async () => {
        const user = await AsyncStorage.retrieveData<JwtUser>("persisted-user");

        const token = user?.token;
        return token ? { "x-auth-token": token } : {};
      },
    }),
  ],
});

export const rawTrpcClient = createTRPCProxyClient<AppRouter>({
  transformer: SuperJSON,
  links: [
    httpBatchLink({
      url: trpcUrl,
      headers: async () => {
        const user = await AsyncStorage.retrieveData<JwtUser>("persisted-user");

        const token = user?.token;
        return token ? { "x-auth-token": token } : {};
      },
    }),
  ],
});

const RedirectContext = createContext<
  [string | null, React.Dispatch<React.SetStateAction<string | null>>] | null
>(null);

export const useRedirectContext = () => {
  const context = useContext(RedirectContext);
  if (context === null) {
    throw new Error(
      "useRedirectContext must be used within a RedirectProvider"
    );
  }
  return context;
};

export default function RootProvider({ children }: React.PropsWithChildren) {
  const state = useState<string | null>(null);

  return (
    <>
      <StatusBar animated style="dark" />

      <RedirectContext.Provider value={state}>
        <AppInstanceIdProvider>
          <FontsProvider>
            <trpc.Provider client={trpcClient} queryClient={queryClient}>
              <QueryClientProvider client={queryClient}>
                <AlertProvider />
                <AppProvider>{children}</AppProvider>
                <Tracker />
              </QueryClientProvider>
            </trpc.Provider>
          </FontsProvider>
        </AppInstanceIdProvider>
      </RedirectContext.Provider>
      <Toast />
    </>
  );
}
